<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Emergency Communications Platform</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700&amp;family=Roboto:wght@300;400;500;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Outlined" rel="stylesheet"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.5/gsap.min.js"></script>
<script type="importmap">
      {
        "imports": {
          "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
          "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
        }
      }
    </script>
<style>
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #0a192f;color: #e6f1ff;overflow: hidden;perspective: 1000px;}
        .font-orbitron {
            font-family: 'Orbitron', sans-serif;
        }
        .glassmorphic-card {
            background: rgba(255, 255, 255, 0.1);backdrop-filter: blur(10px) saturate(180%);
            -webkit-backdrop-filter: blur(10px) saturate(180%);
            border-radius: 0.75rem;border: 1px solid rgba(209, 213, 219, 0.2);box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);transform-style: preserve-3d;}
        .panel-header {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid rgba(209, 213, 219, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .panel-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.125rem;font-weight: 500;
            color: #93c5fd;}
        .btn-source {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(5px) saturate(150%);
            -webkit-backdrop-filter: blur(5px) saturate(150%);
            color: #e0f2fe;padding: 0.75rem 1rem;
            border-radius: 0.375rem;font-weight: 500;
            transition: background-color 0.3s ease, transform 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;border: 1px solid rgba(209, 213, 219, 0.2);
        }
        .btn-source:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }
        .data-display-card {
            background: rgba(10, 25, 47, 0.7);
            border: 1px solid #1e429f;border-radius: 0.375rem;
            padding: 1rem;
            min-height: 120px;}
        #map-container-3d {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 0;}
        .icon-cap { color: #f59e0b; }.icon-meteo { color: #38bdf8; }.icon-ghaap { color: #22c55e; }.icon-nadmo { color: #ef4444; }.icon-fire { color: #f97316; }.icon-police { color: #3b82f6; }.advisory-card {
            transform: translateZ(20px);}
        .data-sources-panel {
            transform: translateZ(30px) rotateY(-5deg);}
        .advisory-panel {
            transform: translateZ(30px) rotateY(5deg);}
        .main-grid > div {
            min-height: 80vh;}
    </style>
</head>
<body class="p-4 lg:p-8 min-h-screen flex flex-col">
<header class="text-center mb-6 lg:mb-8">
<h1 class="font-orbitron text-3xl lg:text-4xl font-bold text-sky-300 tracking-wider" style="transform: translateZ(50px);">EMERGENCY COMMUNICATIONS PLATFORM</h1>
</header>
<div class="flex-grow grid grid-cols-1 lg:grid-cols-5 gap-6 main-grid items-stretch">
<div class="glassmorphic-card lg:col-span-1 data-sources-panel flex flex-col">
<div class="panel-header">
<h2 class="panel-title">DATA SOURCES</h2>
</div>
<div class="p-4 space-y-3 overflow-y-auto">
<button class="btn-source w-full">
<span class="material-icons-outlined icon-cap">campaign</span>
                    CAP Alert Editor
                </button>
<button class="btn-source w-full">
<span class="material-icons-outlined icon-meteo">thermostat</span>
                    OpenMeteo API
                </button>
<button class="btn-source w-full">
<span class="material-icons-outlined icon-ghaap">agriculture</span>
                    Ghaap.com Advisory
                </button>
<button class="btn-source w-full">
<span class="material-icons-outlined icon-nadmo">local_police</span>
                    NADMO
                </button>
<button class="btn-source w-full">
<span class="material-icons-outlined icon-fire">local_fire_department</span>
                    Fire Service
                </button>
<button class="btn-source w-full">
<span class="material-icons-outlined icon-police">shield</span>
                    Ghana Police
                </button>
</div>
</div>
<div class="lg:col-span-3 relative flex items-center justify-center" style="z-index: -10; transform-style: preserve-3d;">
<div id="map-container-3d" style="transform: rotateX(25deg) rotateY(0deg) rotateZ(-5deg) scale(0.9) translateZ(-50px);">
</div>
<img alt="Map of Ghana showing target area" class="absolute inset-0 w-full h-full object-contain opacity-30" src="https://lh3.googleusercontent.com/aida-public/AB6AXuA69qahvC_YnG9zmHvxTtQ_LgPB-MwS6GFfVfTBw3pYetBQXFR7OWbE5TGXR9M2iq4-NRv5fwHilMM6ZK9b3M86LV2HtIQYPgEy9D9JS8d_0inNu9Wv8uqjHBRIw8_B98Su21mBmLaqmcHLcfxSVT3nsVOhG-21uL8YdZSCxa1ampGsIGlMC5YDL1eIonvD0_61uHM9DKODESA0zNUOcUR1472KLg3PtAbxKh_F2kkHfzpo5p_phNwazF-tbfKn3ffh2bZqDmD9kFlP" style="transform: translateZ(-60px)"/>
</div>
<div class="glassmorphic-card lg:col-span-1 advisory-panel flex flex-col">
<div class="panel-header">
<h2 class="panel-title">ADVISORIES</h2>
<div class="flex items-center space-x-2">
<span class="material-icons-outlined text-sky-300 text-lg">location_on</span>
<span class="text-sm text-sky-300">Accra Region</span>
</div>
</div>
<div class="p-4 space-y-4 overflow-y-auto">
<div class="glassmorphic-card p-4 advisory-card">
<h3 class="font-orbitron text-lg text-amber-400 mb-2 flex items-center">
<span class="material-icons-outlined mr-2 icon-cap">warning</span>Weather Emergency
                    </h3>
<div class="data-display-card">
<p class="text-sm text-slate-300">Heavy rainfall expected in the Greater Accra Region for the next 24 hours. Potential for localized flooding.</p>
<p class="text-xs text-slate-400 mt-2">Source: OpenMeteo API | Updated: 10:30 AM GMT</p>
</div>
</div>
<div class="glassmorphic-card p-4 advisory-card">
<h3 class="font-orbitron text-lg text-green-400 mb-2 flex items-center">
<span class="material-icons-outlined mr-2 icon-ghaap">eco</span>Agro-Climate Advisory
                    </h3>
<div class="data-display-card">
<p class="text-sm text-slate-300">Favorable conditions for planting maize. Farmers are advised to monitor soil moisture levels.</p>
<p class="text-xs text-slate-400 mt-2">Source: Ghaap.com | Updated: Yesterday</p>
</div>
</div>
<div class="glassmorphic-card p-4 advisory-card">
<h3 class="font-orbitron text-lg text-red-400 mb-2 flex items-center">
<span class="material-icons-outlined mr-2 icon-nadmo">dangerous</span>Security Alert
                    </h3>
<div class="data-display-card">
<p class="text-sm text-slate-300">Increased patrols in Madina area due to recent incidents. Exercise caution after dark.</p>
<p class="text-xs text-slate-400 mt-2">Source: Ghana Police | Updated: 1:00 PM GMT</p>
</div>
</div>
</div>
</div>
</div>
<script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        let scene, camera, renderer, controls;
        let rainParticles, rainGeo, rainMaterial;
        let sunLight, sunSphere;
        function init3D() {
            const container = document.getElementById('map-container-3d');
            if (!container) {
                console.error("Map container not found");
                return;
            }
            // Scene
            scene = new THREE.Scene();
            scene.fog = new THREE.FogExp2(0x0a192f, 0.005); // Match body background
            // Camera
            camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.set(0, 20, 40); // Angled view
            camera.lookAt(0,0,0);
            // Renderer
            renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            container.appendChild(renderer.domElement);
            // Controls (optional, for debugging)
            // controls = new OrbitControls(camera, renderer.domElement);
            // controls.enableDamping = true;
            // controls.dampingFactor = 0.05;
            // controls.screenSpacePanning = false;
            // controls.minDistance = 20;
            // controls.maxDistance = 100;
            // controls.maxPolarAngle = Math.PI / 2.2; // Prevent looking from below
            // Lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 2); // Soft white light
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5);
            directionalLight.position.set(5, 10, 7.5);
            scene.add(directionalLight);
            // Placeholder for Ghana Map (e.g., a simple plane)
            // In a real scenario, you might load a 3D model of Ghana or a more detailed plane texture
            const mapGeometry = new THREE.PlaneGeometry(50, 30); // Adjust size as needed
            const mapMaterial = new THREE.MeshStandardMaterial({
                color: 0x1a365d, // Darker blue for land
                transparent: true,
                opacity:0.6,
                roughness: 0.8,
                metalness: 0.2
            });
            const mapPlane = new THREE.Mesh(mapGeometry, mapMaterial);
            mapPlane.rotation.x = -Math.PI / 2; // Lay flat
            mapPlane.position.y = -5; // Slightly below center
            scene.add(mapPlane);
            // Initial weather effect (e.g., rain)
            createRainEffect();
            // createSunEffect(); // Example: call this to show sun
            animate();
        }
        function createRainEffect() {
            if (sunLight) scene.remove(sunLight);
            if (sunSphere) scene.remove(sunSphere);
            rainGeo = new THREE.BufferGeometry();
            const rainCount = 10000;
            const positions = new Float32Array(rainCount * 3);
            for (let i = 0; i < rainCount * 3; i += 3) {
                positions[i] = Math.random() * 80 - 40;     // x
                positions[i+1] = Math.random() * 100 - 20;  // y (start higher)
                positions[i+2] = Math.random() * 80 - 40;   // z
            }
            rainGeo.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            rainMaterial = new THREE.PointsMaterial({
                color: 0xaaaaaa,
                size: 0.2,
                transparent: true,
                opacity: 0.7
            });
            rainParticles = new THREE.Points(rainGeo, rainMaterial);
            scene.add(rainParticles);
        }
        function createSunEffect() {
            if (rainParticles) scene.remove(rainParticles);
            sunLight = new THREE.PointLight(0xffddaa, 2, 200);
            sunLight.position.set(0, 50, 0);
            scene.add(sunLight);
            // Optional: Visual representation of the sun
            const sunGeo = new THREE.SphereGeometry(5, 32, 32);
            const sunMat = new THREE.MeshBasicMaterial({ color: 0xffddaa, fog:false });
            sunSphere = new THREE.Mesh(sunGeo, sunMat);
            sunSphere.position.copy(sunLight.position);
            scene.add(sunSphere);
             // Add god rays / volumetric light (simplified)
            const lensflare = new THREE.TextureLoader().load('https://unpkg.com/three@0.158.0/examples/textures/lensflare/lensflare0.png');
            const flareMaterial = new THREE.SpriteMaterial({
                map: lensflare,
                color: 0xffddaa,
                transparent: true,
                opacity: 0.3,
                blending: THREE.AdditiveBlending
            });
            for (let i = 0; i < 5; i++) {
                const sprite = new THREE.Sprite(flareMaterial);
                sprite.scale.set(60, 60, 1);
                sprite.position.copy(sunLight.position);
                sprite.position.x += (Math.random() - 0.5) * 10;
                sprite.position.z += (Math.random() - 0.5) * 10;
                scene.add(sprite);
            }
        }
        function animate() {
            requestAnimationFrame(animate);
            if (rainParticles && rainGeo) {
                const positions = rainGeo.attributes.position.array;
                for (let i = 0; i < positions.length; i += 3) {
                    positions[i+1] -= 0.5; // Raindrop speed
                    if (positions[i+1] < -20) { // Reset raindrop when it hits "ground"
                        positions[i+1] = 80;
                    }
                }
                rainGeo.attributes.position.needsUpdate = true;
            }
            if (sunSphere) {
                sunSphere.rotation.y += 0.005;
            }
            // if (controls) controls.update();
            renderer.render(scene, camera);
        }
        // GSAP Animations for panels
        function initGSAPAnimations() {
            gsap.from(".data-sources-panel", {
                duration: 1,
                x: -200,
                opacity: 0,
                rotationY: 15,
                delay: 0.2,
                ease: "power3.out"
            });
            gsap.from(".advisory-panel", {
                duration: 1,
                x: 200,
                opacity: 0,
                rotationY: -15,
                delay: 0.2,
                ease: "power3.out"
            });
            gsap.from("header h1", {
                duration: 1,
                y: -50,
                z: -100, // For 3D effect
                opacity: 0,
                delay: 0.1,
                ease: "power3.out"
            });
            gsap.from("#map-container-3d", {
                duration: 1.5,
                opacity: 0,
                scale: 0.8,
                z: -200, // Push back further initially
                delay: 0.5,
                ease: "elastic.out(1, 0.5)"
            });
            document.querySelectorAll('.btn-source, .advisory-card').forEach((el, index) => {
                gsap.from(el, {
                    duration: 0.5,
                    opacity: 0,
                    y: 20,
                    delay: 0.5 + index * 0.05,
                    ease: "power2.out"
                });
            });
        }
        // Handle window resize
        window.addEventListener('resize', () => {
            const container = document.getElementById('map-container-3d');
            if (camera && renderer && container) {
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            }
        });
        // Initialization
        init3D();
        initGSAPAnimations();
        // Example: Switch to sun effect after 5 seconds
        // setTimeout(() => {
        //    createSunEffect();
        // }, 5000);
    </script>

</body></html>