<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Communications Platform - Ghana</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0f;
            color: #fff;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }

        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }

        .header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            padding: 20px;
            text-align: center;
            z-index: 20;
            pointer-events: auto;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            letter-spacing: 3px;
            text-transform: uppercase;
            background: linear-gradient(90deg, #00d4ff, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            pointer-events: auto;
        }

        .glass-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .data-sources {
            position: absolute;
            bottom: 30px;
            left: 30px;
            z-index: 15;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .source-button {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 20px;
            cursor: pointer;
            min-width: 250px;
            border: none;
            color: #fff;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .source-button.active {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.5);
        }

        .source-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .cap-alert { background: linear-gradient(135deg, #f39c12, #e74c3c); }
        .meteo { background: linear-gradient(135deg, #3498db, #2980b9); }
        .agro { background: linear-gradient(135deg, #27ae60, #16a085); }
        .nadmo { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .fire { background: linear-gradient(135deg, #e67e22, #d35400); }
        .police { background: linear-gradient(135deg, #34495e, #2c3e50); }

        .advisory-panel {
            position: absolute;
            bottom: 30px;
            right: 30px;
            z-index: 15;
            max-width: 350px;
        }

        .advisory-card {
            margin-bottom: 20px;
        }

        .advisory-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .advisory-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .weather-icon { background: linear-gradient(135deg, #3498db, #2980b9); }
        .agro-icon { background: linear-gradient(135deg, #27ae60, #16a085); }

        .advisory-content h3 {
            font-size: 1.1rem;
            margin-bottom: 5px;
            color: #00d4ff;
        }

        .advisory-content p {
            font-size: 0.9rem;
            line-height: 1.5;
            opacity: 0.8;
        }

        .status-bar {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 15;
            display: flex;
            gap: 30px;
            padding: 15px 30px;
        }

        .status-item {
            text-align: center;
        }

        .status-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #00d4ff;
        }

        .status-label {
            font-size: 0.8rem;
            opacity: 0.7;
            text-transform: uppercase;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #0a0a0f;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: opacity 0.5s ease;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(0, 212, 255, 0.3);
            border-top: 3px solid #00d4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loading">
        <div class="loading-spinner"></div>
    </div>

    <div id="canvas-container"></div>

    <div class="overlay">
        <div class="header">
            <h1>Emergency Communications Platform</h1>
        </div>

        <div class="status-bar glass-card">
            <div class="status-item">
                <div class="status-value" id="active-alerts">0</div>
                <div class="status-label">Active Alerts</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="coverage">0</div>
                <div class="status-label">Coverage Area (km²)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="response-time">--</div>
                <div class="status-label">Response Time</div>
            </div>
        </div>

        <div class="data-sources">
            <button class="source-button glass-card" data-source="cap" id="btn-cap">
                <div class="source-icon cap-alert">⚡</div>
                <span>CAP Alert Editor</span>
            </button>
            <button class="source-button glass-card" data-source="meteo" id="btn-meteo">
                <div class="source-icon meteo">🌤️</div>
                <span>Open Meteo API</span>
            </button>
            <button class="source-button glass-card" data-source="agro" id="btn-agro">
                <div class="source-icon agro">🌾</div>
                <span>GHAAP Agro-Climate</span>
            </button>
            <button class="source-button glass-card" data-source="nadmo" id="btn-nadmo">
                <div class="source-icon nadmo">🚨</div>
                <span>NADMO</span>
            </button>
            <button class="source-button glass-card" data-source="fire" id="btn-fire">
                <div class="source-icon fire">🚒</div>
                <span>Fire Service</span>
            </button>
            <button class="source-button glass-card" data-source="police" id="btn-police">
                <div class="source-icon police">👮</div>
                <span>Ghana Police</span>
            </button>
        </div>

        <div class="advisory-panel">
            <div class="advisory-card glass-card" id="weather-advisory">
                <div class="advisory-header">
                    <div class="advisory-icon weather-icon">🌧️</div>
                    <div class="advisory-content">
                        <h3>Weather Advisory</h3>
                        <p id="weather-text">Awaiting data...</p>
                    </div>
                </div>
            </div>
            <div class="advisory-card glass-card" id="agro-advisory">
                <div class="advisory-header">
                    <div class="advisory-icon agro-icon">🌱</div>
                    <div class="advisory-content">
                        <h3>Agro-Climate Advisory</h3>
                        <p id="agro-text">Awaiting data...</p>
                    </div>
                </div>
            </div>
             <div class="advisory-card glass-card" id="cap-alert-display" style="display: none;">
                <div class="advisory-header">
                    <div class="advisory-icon cap-alert">⚡</div>
                    <div class="advisory-content">
                        <h3 id="cap-headline">CAP Alert</h3>
                        <p id="cap-description">Awaiting data...</p>
                        <small id="cap-area" style="display: block; margin-top: 5px; opacity: 0.7;"></small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script>
        // --- Three.js Scene Setup ---
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        document.getElementById('canvas-container').appendChild(renderer.domElement);

        // Lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.7); 
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.9);
        directionalLight.position.set(15, 20, 10); 
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048; 
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        scene.add(directionalLight);
        
        // --- Ghana Map from GeoJSON ---
        let ghanaMapGroup; 
        // Use a simple relative path string first. If it fails, the URL constructor might still work.
        const GEOJSON_FILE_NAME = 'ghana_regions.json'; 

        async function loadAndCreateGhanaMap() {
            let geoJsonPath;
            try {
                // Construct an absolute URL from the relative file name and the current page's URL
                geoJsonPath = new URL(GEOJSON_FILE_NAME, window.location.href).href;
                console.log(`Attempting to fetch GeoJSON from: ${geoJsonPath}`);
                const response = await fetch(geoJsonPath);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status} when fetching ${geoJsonPath}`);
                }
                const geoJsonData = await response.json();
                createGhanaMapFromGeoJSON(geoJsonData);
            } catch (error) {
                console.error(`Could not load or parse GeoJSON data from ${geoJsonPath || GEOJSON_FILE_NAME}:`, error);
                createFallbackMap();
            }
        }

        function createFallbackMap() {
            console.warn("Creating fallback map due to GeoJSON loading error.");
            ghanaMapGroup = new THREE.Group(); 
            const fallbackMaterial = new THREE.MeshPhongMaterial({ color: 0x555555 });
            const fallbackGeom = new THREE.BoxGeometry(5,0.5,5);
            const fallbackMesh = new THREE.Mesh(fallbackGeom, fallbackMaterial);
            fallbackMesh.position.y = -0.5;
            ghanaMapGroup.add(fallbackMesh);
            scene.add(ghanaMapGroup);
        }


        function createGhanaMapFromGeoJSON(geoJsonData) {
            if (!geoJsonData || !geoJsonData.features || geoJsonData.features.length === 0) {
                console.error("GeoJSON data is empty or invalid.");
                createFallbackMap();
                return;
            }

            ghanaMapGroup = new THREE.Group();
            const regionMaterial = new THREE.MeshPhongMaterial({
                color: 0x006A4E, 
                shininess: 15,
                side: THREE.DoubleSide,
            });
            const outlineMaterial = new THREE.LineBasicMaterial({ color: 0xFCD116, linewidth: 2 }); 

            const allPoints = [];
            geoJsonData.features.forEach(feature => {
                if (feature.geometry && feature.geometry.coordinates) {
                    const polygons = feature.geometry.type === 'Polygon' 
                        ? [feature.geometry.coordinates] 
                        : feature.geometry.coordinates;

                    polygons.forEach(polygonCoordsArray => {
                        const exteriorRing = polygonCoordsArray[0];
                        exteriorRing.forEach(coord => {
                            if (coord && typeof coord[0] === 'number' && typeof coord[1] === 'number') {
                                allPoints.push(new THREE.Vector2(coord[0], coord[1]));
                            } else {
                                console.warn("Invalid coordinate found in GeoJSON:", coord, "Feature:", feature.properties.region);
                            }
                        });
                    });
                }
            });

            if (allPoints.length === 0) {
                console.error("No valid coordinates found in GeoJSON features to create map.");
                createFallbackMap();
                return;
            }

            const boundingBox = new THREE.Box2().setFromPoints(allPoints);
            const center = boundingBox.getCenter(new THREE.Vector2());
            const size = boundingBox.getSize(new THREE.Vector2());
            
            const maxDim = Math.max(size.x, size.y);
            const sceneScaleFactor = maxDim > 0 ? (10 / maxDim) : 1; 

            geoJsonData.features.forEach(feature => {
                if (feature.geometry && feature.geometry.coordinates) {
                    const polygons = feature.geometry.type === 'Polygon' 
                        ? [feature.geometry.coordinates]
                        : feature.geometry.coordinates;

                    polygons.forEach((polygonCoordsArray, polyIndex) => {
                        const exteriorRingCoords = polygonCoordsArray[0];
                        if (!exteriorRingCoords || exteriorRingCoords.length < 3) {
                            console.warn("Skipping invalid exterior ring for region:", feature.properties.region, "Polygon index:", polyIndex);
                            return; 
                        }

                        const regionShape = new THREE.Shape();
                        const firstPoint = exteriorRingCoords[0];
                        if (!firstPoint || typeof firstPoint[0] !== 'number' || typeof firstPoint[1] !== 'number') {
                             console.warn("Skipping shape due to invalid first point for region:", feature.properties.region);
                             return;
                        }
                        regionShape.moveTo(
                            (firstPoint[0] - center.x) * sceneScaleFactor,
                            (firstPoint[1] - center.y) * sceneScaleFactor
                        );

                        for (let i = 1; i < exteriorRingCoords.length; i++) {
                            const point = exteriorRingCoords[i];
                             if (!point || typeof point[0] !== 'number' || typeof point[1] !== 'number') {
                                console.warn("Skipping point due to invalid coordinate for region:", feature.properties.region);
                                continue;
                            }
                            regionShape.lineTo(
                                (point[0] - center.x) * sceneScaleFactor,
                                (point[1] - center.y) * sceneScaleFactor
                            );
                        }

                        for (let h = 1; h < polygonCoordsArray.length; h++) {
                            const holeCoords = polygonCoordsArray[h];
                             if (!holeCoords || holeCoords.length < 3) continue;

                            const holePath = new THREE.Path();
                            const firstHolePoint = holeCoords[0];
                            if (!firstHolePoint || typeof firstHolePoint[0] !== 'number' || typeof firstHolePoint[1] !== 'number') continue;

                            holePath.moveTo(
                                (firstHolePoint[0] - center.x) * sceneScaleFactor,
                                (firstHolePoint[1] - center.y) * sceneScaleFactor
                            );
                            for (let i = 1; i < holeCoords.length; i++) {
                                const holePoint = holeCoords[i];
                                if (!holePoint || typeof holePoint[0] !== 'number' || typeof holePoint[1] !== 'number') continue;
                                holePath.lineTo(
                                    (holePoint[0] - center.x) * sceneScaleFactor,
                                    (holePoint[1] - center.y) * sceneScaleFactor
                                );
                            }
                            regionShape.holes.push(holePath);
                        }


                        const extrudeSettings = {
                            steps: 1,
                            depth: 0.3, 
                            bevelEnabled: true,
                            bevelThickness: 0.05,
                            bevelSize: 0.05,
                            bevelOffset: 0,
                            bevelSegments: 1
                        };

                        const geometry = new THREE.ExtrudeGeometry(regionShape, extrudeSettings);
                        
                        const regionMesh = new THREE.Mesh(geometry, regionMaterial.clone()); 
                        regionMesh.castShadow = true;
                        regionMesh.receiveShadow = true;
                        regionMesh.name = feature.properties.region || `region-${polyIndex}`; 
                        ghanaMapGroup.add(regionMesh);

                        const edgesGeom = new THREE.EdgesGeometry(geometry);
                        const regionOutline = new THREE.LineSegments(edgesGeom, outlineMaterial.clone());
                        ghanaMapGroup.add(regionOutline);
                    });
                }
            });

            ghanaMapGroup.rotation.x = -Math.PI / 2; 
            ghanaMapGroup.position.y = -0.5; 
            scene.add(ghanaMapGroup);
            console.log("Ghana map created from GeoJSON data.");
        }

        loadAndCreateGhanaMap(); 

        let currentWeatherEffect = null; 

        camera.position.set(0, 10, 15); 
        camera.lookAt(0,0,0); 

        // --- UI Interaction & Data Handling ---
        const sourceButtons = document.querySelectorAll('.source-button');
        const mockData = { 
            cap: { alerts: 0, coverage: 0, responseTime: '--', headline: 'CAP Alert', description: 'Awaiting data...', area: ''},
            meteo: { alerts: 0, coverage: 0, responseTime: '--', weatherAdvisory: 'Awaiting data...', sunny: false },
            agro: { alerts: 0, coverage: 0, responseTime: '--', agroAdvisory: 'Awaiting data...' },
            nadmo: { alerts: 0, coverage: 0, responseTime: '--', weatherAdvisory: 'Awaiting data...' },
            fire: { alerts: 0, coverage: 0, responseTime: '--', weatherAdvisory: 'Awaiting data...' },
            police: { alerts: 0, coverage: 0, responseTime: '--', weatherAdvisory: 'Awaiting data...' }
        };

        function updateUI(sourceKey, data) {
            const currentSourceData = data || (mockData[sourceKey] || {});
            document.getElementById('active-alerts').textContent = currentSourceData.alerts !== undefined ? currentSourceData.alerts : 0;
            document.getElementById('coverage').textContent = currentSourceData.coverage !== undefined ? currentSourceData.coverage : 0;
            document.getElementById('response-time').textContent = currentSourceData.responseTime || '--';

            const weatherAdvisoryEl = document.getElementById('weather-advisory');
            const agroAdvisoryEl = document.getElementById('agro-advisory');
            const capDisplayEl = document.getElementById('cap-alert-display');

            weatherAdvisoryEl.style.display = 'none';
            agroAdvisoryEl.style.display = 'none';
            capDisplayEl.style.display = 'none';

            if (sourceKey === 'meteo' && currentSourceData.weatherAdvisory) {
                weatherAdvisoryEl.style.display = 'block';
                document.getElementById('weather-text').textContent = currentSourceData.weatherAdvisory;
            } else if (sourceKey === 'agro' && currentSourceData.agroAdvisory) {
                agroAdvisoryEl.style.display = 'block';
                document.getElementById('agro-text').textContent = currentSourceData.agroAdvisory;
            } else if (sourceKey === 'cap' && currentSourceData.headline) {
                capDisplayEl.style.display = 'block';
                document.getElementById('cap-headline').textContent = currentSourceData.headline;
                document.getElementById('cap-description').textContent = currentSourceData.description;
                document.getElementById('cap-area').textContent = currentSourceData.area || "";
                document.getElementById('cap-area').style.display = currentSourceData.area ? 'block' : 'none';
            } else if (currentSourceData.weatherAdvisory) { 
                 weatherAdvisoryEl.style.display = 'block';
                 document.getElementById('weather-text').textContent = currentSourceData.weatherAdvisory;
            }
            
            gsap.from('.status-value', { innerText: 0, duration: 0.5, snap: { innerText: 1 }, stagger: 0.1, ease: 'power2.out' });
            gsap.fromTo('.advisory-card:not([style*="display: none"])', {opacity: 0, y:20}, {opacity:1, y:0, duration:0.5, stagger:0.1});
        }
        
        sourceButtons.forEach(button => {
            button.addEventListener('click', () => {
                const source = button.dataset.source;
                sourceButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                console.log(`Button clicked: ${source}. Fetching data...`);
                updateUI(source, mockData[source] || {});
            });
        });

        function animate() {
            requestAnimationFrame(animate);
            if (ghanaMapGroup) { 
                ghanaMapGroup.rotation.y += 0.001; 
            }
            renderer.render(scene, camera);
        }

        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        animate(); 

        setTimeout(() => {
            document.getElementById('loading').classList.add('hidden');
            gsap.from('.header h1', { y: -50, opacity: 0, duration: 1, ease: 'power3.out' });
            gsap.from('.glass-card', { scale: 0.8, opacity: 0, duration: 0.8, stagger: 0.1, ease: 'power3.out', delay: 0.3 });
            
            updateUI('cap', mockData.cap); 
            document.getElementById('weather-advisory').style.display = 'block'; 
            document.getElementById('agro-advisory').style.display = 'block'; 
        }, 1000);

    </script>
</body>
</html>
