<!DOCTYPE html>
<html>
<head>
    <title>3D Map of Ghana</title>
    <meta charset="utf-8">
    <style>
        body { margin: 0; overflow: hidden; } /* Prevent scrollbars */
        canvas { display: block; }
    </style>
</head>
<body>
    <!-- Three.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- D3.js for GeoJSON projection (optional but very helpful) -->
    <script src="https://cdn.jsdelivr.net/npm/d3-geo@3"></script>
    <!-- GSAP for animations (optional) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <!-- Your main script (as a module to use imports if needed) -->
    <script type="module" src="main.js"></script>
</body>
</html>