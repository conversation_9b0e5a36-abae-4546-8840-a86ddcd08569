import requests
import uuid
from datetime import datetime, timezone, timedelta
import xml.etree.ElementTree as ET
import time
import json 

# --- Configuration ---

LATITUDE = 5.55602 # Accra's coordinates
LONGITUDE = -0.1969 

LOCATION_DESCRIPTION = "Accra, Greater Accra Region, Ghana"

# CAP Message Sender Information
SENDER_ID = "<EMAIL>" # An identifier for the sender
SENDER_NAME = "Emergency Information Center" # A human-readable sender name


UPDATE_INTERVAL_SECONDS = 3600

# Open-Meteo API URL for current weather
WEATHER_API_URL = "https://api.open-meteo.com/v1/forecast"

# --- Helper Functions ---

def get_utc_timestamp():
    """Returns the current UTC timestamp in ISO 8601 format compatible with CAP."""
    return datetime.now(timezone.utc).isoformat(timespec='seconds')

def fetch_weather_data(lat, lon):
    """
    Fetches current weather data from Open-Meteo API.
    Returns a dictionary with weather information or None if an error occurs.
    """
    params = {
        "latitude": lat,
        "longitude": lon,
        "current_weather": "true", # Request current weather
        "hourly": "temperature_2m,relativehumidity_2m,apparent_temperature,precipitation,weathercode,windspeed_10m,winddirection_10m",
        "timezone": "auto" 
    }
    try:
        response = requests.get(WEATHER_API_URL, params=params, timeout=10)
        response.raise_for_status()  
        data = response.json()

        if data.get("current_weather"):
            current = data["current_weather"]
            
            weather_info = {
                "temperature": current.get("temperature"),
                "windspeed": current.get("windspeed"),
                "winddirection": current.get("winddirection"),
                "weathercode": current.get("weathercode"),
                "time": current.get("time"),

                "humidity": None 
            }
            
            # Attempting to get the latest humidity from hourly data
            if data.get("hourly") and data["hourly"].get("time") and data["hourly"].get("relativehumidity_2m"):
                hourly_times = data["hourly"]["time"]
                hourly_humidity = data["hourly"]["relativehumidity_2m"]
                

                try:
                    current_obs_time = datetime.fromisoformat(current.get("time"))
                    closest_time_diff = timedelta.max
                    humidity_value = None

                    for i, h_time_str in enumerate(hourly_times):
                        h_time = datetime.fromisoformat(h_time_str)
                        time_diff = abs(current_obs_time - h_time)
                        if time_diff < closest_time_diff:
                            closest_time_diff = time_diff
                            humidity_value = hourly_humidity[i]

                        if time_diff < timedelta(minutes=30): # If within 30 mins, good enough
                             break
                    weather_info["humidity"] = humidity_value

                except Exception as e:
                    print(f"Error processing hourly humidity: {e}")


            return weather_info
        else:
            print("Error: 'current_weather' not found in API response.")
            print(f"API Response: {data}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Error fetching weather data: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"Error decoding weather API JSON response: {e}")
        return None
    except KeyError as e:
        print(f"KeyError in weather data: {e} - Check API response structure.")
        return None


def interpret_weather_code(code):
    """Interprets WMO weather codes into human-readable strings."""

    codes = {
        0: "Clear sky",
        1: "Mainly clear", 2: "Partly cloudy", 3: "Overcast",
        45: "Fog", 48: "Depositing rime fog",
        51: "Light drizzle", 53: "Moderate drizzle", 55: "Dense drizzle",
        56: "Light freezing drizzle", 57: "Dense freezing drizzle",
        61: "Slight rain", 63: "Moderate rain", 65: "Heavy rain",
        66: "Light freezing rain", 67: "Heavy freezing rain",
        71: "Slight snow fall", 73: "Moderate snow fall", 75: "Heavy snow fall",
        77: "Snow grains",
        80: "Slight rain showers", 81: "Moderate rain showers", 82: "Violent rain showers",
        85: "Slight snow showers", 86: "Heavy snow showers",
        95: "Thunderstorm: Slight or moderate", # In CAP, might be more specific
        96: "Thunderstorm with slight hail", 99: "Thunderstorm with heavy hail"
    }
    return codes.get(code, f"Unknown weather code: {code}")


def create_cap_message(weather_info):
    """
    Creates a CAP XML message string from weather information.
    """
    if not weather_info:
        return None

    current_time_utc_str = get_utc_timestamp()

    try:
        observation_time_dt = datetime.fromisoformat(weather_info["time"])

        observation_time_utc_dt = observation_time_dt.astimezone(timezone.utc)
        effective_time_str = observation_time_utc_dt.isoformat(timespec='seconds')
    except Exception as e:
        print(f"Error parsing observation time '{weather_info['time']}': {e}. Using current UTC time as effective time.")
        effective_time_str = current_time_utc_str


    # Message expiry (e.g., 1 hour from now for hourly updates)
    expires_dt = datetime.fromisoformat(current_time_utc_str.replace('Z', '+00:00')) + timedelta(seconds=UPDATE_INTERVAL_SECONDS)
    expires_time_str = expires_dt.isoformat(timespec='seconds')


    # --- Build CAP Message ---
    alert = ET.Element("alert", xmlns="urn:oasis:names:tc:emergency:cap:1.2")

    ET.SubElement(alert, "identifier").text = str(uuid.uuid4())
    ET.SubElement(alert, "sender").text = SENDER_ID
    ET.SubElement(alert, "sent").text = current_time_utc_str
    ET.SubElement(alert, "status").text = "Actual"  # For observed weather
    ET.SubElement(alert, "msgType").text = "Info"    # For informational updates
    ET.SubElement(alert, "scope").text = "Public"

    # --- Info Block ---
    info = ET.SubElement(alert, "info")
    ET.SubElement(info, "language").text = "en-US"
    ET.SubElement(info, "category").text = "Met" # Meteorological
    
    weather_description_str = interpret_weather_code(weather_info.get("weathercode"))
    event_text = f"Hourly Weather Report: {weather_description_str}"
    ET.SubElement(info, "event").text = event_text

    ET.SubElement(info, "urgency").text = "Unknown" # Routine updates are not typically "Immediate"
    ET.SubElement(info, "severity").text = "Unknown" # Not a severe event
    ET.SubElement(info, "certainty").text = "Observed"

    ET.SubElement(info, "effective").text = effective_time_str
    ET.SubElement(info, "expires").text = expires_time_str
    ET.SubElement(info, "senderName").text = SENDER_NAME
    
    headline_text = (
        f"Weather Update for {LOCATION_DESCRIPTION} at "
        f"{observation_time_dt.strftime('%Y-%m-%d %H:%M %Z')}: "
        f"{weather_description_str}, Temp: {weather_info.get('temperature')}°C"
    )
    ET.SubElement(info, "headline").text = headline_text

    description_text = (
        f"Current weather conditions for {LOCATION_DESCRIPTION} as of {observation_time_dt.strftime('%Y-%m-%d %H:%M %Z')}:\n"
        f"- Condition: {weather_description_str}\n"
        f"- Temperature: {weather_info.get('temperature')}°C\n"
        f"- Wind Speed: {weather_info.get('windspeed')} km/h\n"
        f"- Wind Direction: {weather_info.get('winddirection')}°\n"
    )
    if weather_info.get("humidity") is not None:
         description_text += f"- Humidity: {weather_info.get('humidity')}%\n"
    else:
        description_text += "- Humidity: Not available\n"

    ET.SubElement(info, "description").text = description_text
    

    # --- Area Block ---
    area = ET.SubElement(info, "area")
    ET.SubElement(area, "areaDesc").text = LOCATION_DESCRIPTION

    cap_xml_string = ET.tostring(alert, encoding="unicode")
    
    return cap_xml_string

# --- Main Loop ---
if __name__ == "__main__":
    print(f"Starting CAP message generator for {LOCATION_DESCRIPTION}.")
    print(f"Fetching weather and generating messages every {UPDATE_INTERVAL_SECONDS} seconds.")
    print("Press Ctrl+C to stop.")

    try:
        while True:
            print(f"\n[{get_utc_timestamp()}] Fetching new weather data...")
            weather_data = fetch_weather_data(LATITUDE, LONGITUDE)

            if weather_data:
                print(f"Weather data received: {weather_data}")
                cap_message = create_cap_message(weather_data)
                if cap_message:
                    print("\n--- Generated CAP Message ---")
                    print(cap_message)
                    print("--- End of CAP Message ---\n")

                else:
                    print("Failed to generate CAP message.")
            else:
                print("Failed to fetch weather data. Retrying next cycle.")
            
            print(f"Sleeping for {UPDATE_INTERVAL_SECONDS} seconds...")
            time.sleep(UPDATE_INTERVAL_SECONDS)

    except KeyboardInterrupt:
        print("\nGenerator stopped by user.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

