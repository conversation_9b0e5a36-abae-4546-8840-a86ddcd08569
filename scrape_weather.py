import requests
from bs4 import BeautifulSoup
import pandas as pd
import time

def scrape_ghaap_weather_forecast(url):
    """
    Scrapes crop weather forecast data from ghaap.com and returns a pandas DataFrame.
    """
    print(f"Attempting to scrape URL: {url}")

    # Define a User-Agent header to mimic a web browser
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }

    try:
        # Fetch the content of the page
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise an HTTPError for bad responses (4xx or 5xx)

        # Parse the HTML content
        soup = BeautifulSoup(response.text, 'html.parser')

        # Find the table containing the forecast data
        # Inspecting the page, the data is in a table with specific classes.
        table = soup.find('table', class_='table table-striped table-bordered table-hover')

        if not table:
            print("Error: The data table was not found on the page. The page structure might have changed or the URL is incorrect.")
            return pd.DataFrame() # Return an empty DataFrame

        # Extract table headers
        headers = []
        for th in table.find('thead').find_all('th'):
            headers.append(th.get_text(strip=True))

        # Extract table rows
        data_rows = []
        for row in table.find('tbody').find_all('tr'):
            columns = [td.get_text(strip=True) for td in row.find_all('td')]
            if columns: # Ensure we don't add empty rows if any exist
                data_rows.append(columns)

        # Create a pandas DataFrame
        df = pd.DataFrame(data_rows, columns=headers)

        print("Scraping successful!")
        return df

    except requests.exceptions.RequestException as e:
        print(f"An error occurred while fetching the page: {e}")
        return pd.DataFrame() # Return an empty DataFrame
    except Exception as e:
        print(f"An unexpected error occurred during scraping: {e}")
        return pd.DataFrame() # Return an empty DataFrame

# --- Main execution ---
if __name__ == "__main__":
    target_url = "https://ghaap.com/weather-forecast/index.php?p=crop-search-event®iontxt=REG02&districttxt=DS028&crop=CT0000000008&yeartxt=2025"
    output_filename = "ghaap_weather_forecast_data.csv"

    df_result = scrape_ghaap_weather_forecast(target_url)

    if not df_result.empty:
        print("\n--- Scraped Data (first 5 rows) ---")
        print(df_result.head())

        # Save the DataFrame to a CSV file
        df_result.to_csv(output_filename, index=False, encoding='utf-8')
        print(f"\nData successfully saved to {output_filename}")
    else:
        print("No data was scraped. Check the URL or the script.")

    print("\n--- Script finished ---")