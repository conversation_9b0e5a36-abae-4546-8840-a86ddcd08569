<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Communications Platform - Ghana</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0f;
            color: #fff;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }

        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }

        .header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            padding: 20px;
            text-align: center;
            z-index: 20;
            pointer-events: auto;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            letter-spacing: 3px;
            text-transform: uppercase;
            background: linear-gradient(90deg, #00d4ff, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            pointer-events: auto;
        }

        .glass-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .data-sources {
            position: absolute;
            bottom: 30px;
            left: 30px;
            z-index: 15;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .source-button {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 20px;
            cursor: pointer;
            min-width: 250px;
            border: none;
            color: #fff;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .source-button.active {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.5);
        }

        .source-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .cap-alert { background: linear-gradient(135deg, #f39c12, #e74c3c); }
        .meteo { background: linear-gradient(135deg, #3498db, #2980b9); }
        .agro { background: linear-gradient(135deg, #27ae60, #16a085); }
        .nadmo { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .fire { background: linear-gradient(135deg, #e67e22, #d35400); }
        .police { background: linear-gradient(135deg, #34495e, #2c3e50); }

        .advisory-panel {
            position: absolute;
            bottom: 30px;
            right: 30px;
            z-index: 15;
            max-width: 350px;
        }

        .advisory-card {
            margin-bottom: 20px;
        }

        .advisory-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .advisory-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .weather-icon { background: linear-gradient(135deg, #3498db, #2980b9); }
        .agro-icon { background: linear-gradient(135deg, #27ae60, #16a085); }

        .advisory-content h3 {
            font-size: 1.1rem;
            margin-bottom: 5px;
            color: #00d4ff;
        }

        .advisory-content p {
            font-size: 0.9rem;
            line-height: 1.5;
            opacity: 0.8;
        }

        .status-bar {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 15;
            display: flex;
            gap: 30px;
            padding: 15px 30px;
        }

        .status-item {
            text-align: center;
        }

        .status-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #00d4ff;
        }

        .status-label {
            font-size: 0.8rem;
            opacity: 0.7;
            text-transform: uppercase;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #0a0a0f;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: opacity 0.5s ease;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(0, 212, 255, 0.3);
            border-top: 3px solid #00d4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loading">
        <div class="loading-spinner"></div>
    </div>

    <div id="canvas-container"></div>

    <div class="overlay">
        <div class="header">
            <h1>Emergency Communications Platform</h1>
        </div>

        <div class="status-bar glass-card">
            <div class="status-item">
                <div class="status-value" id="active-alerts">0</div>
                <div class="status-label">Active Alerts</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="coverage">0</div>
                <div class="status-label">Coverage Area (km²)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="response-time">--</div>
                <div class="status-label">Response Time</div>
            </div>
        </div>

        <div class="data-sources">
            <button class="source-button glass-card" data-source="cap">
                <div class="source-icon cap-alert">⚡</div>
                <span>CAP Alert Editor</span>
            </button>
            <button class="source-button glass-card" data-source="meteo">
                <div class="source-icon meteo">🌤️</div>
                <span>Open Meteo API</span>
            </button>
            <button class="source-button glass-card" data-source="agro">
                <div class="source-icon agro">🌾</div>
                <span>GHAAP Agro-Climate</span>
            </button>
            <button class="source-button glass-card" data-source="nadmo">
                <div class="source-icon nadmo">🚨</div>
                <span>NADMO</span>
            </button>
            <button class="source-button glass-card" data-source="fire">
                <div class="source-icon fire">🚒</div>
                <span>Fire Service</span>
            </button>
            <button class="source-button glass-card" data-source="police">
                <div class="source-icon police">👮</div>
                <span>Ghana Police</span>
            </button>
        </div>

        <div class="advisory-panel">
            <div class="advisory-card glass-card" id="weather-advisory">
                <div class="advisory-header">
                    <div class="advisory-icon weather-icon">🌧️</div>
                    <div class="advisory-content">
                        <h3>Weather Advisory</h3>
                        <p id="weather-text">Loading weather data...</p>
                    </div>
                </div>
            </div>
            <div class="advisory-card glass-card" id="agro-advisory">
                <div class="advisory-header">
                    <div class="advisory-icon agro-icon">🌱</div>
                    <div class="advisory-content">
                        <h3>Agro-Climate Advisory</h3>
                        <p id="agro-text">Loading agricultural data...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script>
        // Scene setup
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        document.getElementById('canvas-container').appendChild(renderer.domElement);

        // Lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(5, 10, 5);
        directionalLight.castShadow = true;
        scene.add(directionalLight);

        // Ghana map with accurate coordinates
        function createGhanaGeometry() {
            const shape = new THREE.Shape();
            const s = 1.5; // scale factor

            // Points go clockwise, starting from bottom-left coast near Cote d'Ivoire border
            shape.moveTo(-3.15*s, 4.5*s);   // 1. SW Coast (Cape Three Points area)
            shape.lineTo(-3.1*s, 5.0*s);    // 2. Western border with Cote d'Ivoire, moving north
            shape.lineTo(-2.8*s, 7.0*s);    // 3. NW region
            shape.lineTo(-2.4*s, 9.0*s);    // 4. Northern border with Burkina Faso, moving east
            shape.lineTo(-1.0*s, 10.5*s);   // 5. Northernmost area (Pulmakom)
            shape.lineTo(0.5*s, 10.0*s);    // 6. Northern border, continuing east
            shape.lineTo(1.2*s, 8.5*s);     // 7. NE corner area
            shape.lineTo(1.2*s, 7.0*s);     // 8. Eastern border with Togo, moving south
            shape.lineTo(1.0*s, 5.5*s);     // 9. Eastern border, mid section
            shape.lineTo(0.8*s, 4.5*s);     // 10. SE coast (Volta estuary)
            shape.lineTo(0.0*s, 4.5*s);     // 11. South coast (Accra area)
            shape.lineTo(-1.5*s, 4.6*s);    // 12. South coast (Cape Coast/Takoradi)
            shape.closePath();

            const extrudeSettings = {
                depth: 0.5,
                bevelEnabled: true,
                bevelSegments: 3,
                steps: 2,
                bevelSize: 0.05,
                bevelThickness: 0.1
            };

            const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);

            // Add terrain displacement with regional variation
            const positions = geometry.attributes.position;
            for (let i = 0; i < positions.count; i++) {
                const x = positions.getX(i);
                const y = positions.getY(i);
                const z = positions.getZ(i);

                // Create elevation based on position (higher in the north)
                if (z > 0.3) {
                    const elevation = Math.random() * 0.3 + (y > 7 ? (y - 7) * 0.1 : 0);
                    positions.setZ(i, z + elevation);
                }
            }
            geometry.computeVertexNormals();

            return geometry;
        }

        const mapGeometry = createGhanaGeometry();
        const mapMaterial = new THREE.MeshPhongMaterial({
            color: 0x1a1a2e,
            wireframe: false,
            side: THREE.DoubleSide,
            transparent: true,
            opacity: 0.8
        });

        const ghanaMap = new THREE.Mesh(mapGeometry, mapMaterial);
        ghanaMap.rotation.x = -Math.PI / 4;
        ghanaMap.position.y = -2;
        ghanaMap.receiveShadow = true;
        scene.add(ghanaMap);

        // Map outline
        const edges = new THREE.EdgesGeometry(mapGeometry);
        const lineMaterial = new THREE.LineBasicMaterial({ color: 0x00d4ff, linewidth: 2 });
        const wireframe = new THREE.LineSegments(edges, lineMaterial);
        wireframe.rotation.x = -Math.PI / 4;
        wireframe.position.y = -1.95;
        scene.add(wireframe);

        // Weather effects
        let currentWeatherEffect = null;

        function createRainEffect() {
            const rainGroup = new THREE.Group();
            const rainCount = 200;

            for (let i = 0; i < rainCount; i++) {
                const geometry = new THREE.BufferGeometry();
                const positions = new Float32Array(6);

                // Random x and z positions
                const x = (Math.random() - 0.5) * 20;
                const z = (Math.random() - 0.5) * 20;
                const y = Math.random() * 15;

                // Line from top to bottom
                positions[0] = x;
                positions[1] = y;
                positions[2] = z;
                positions[3] = x;
                positions[4] = y - 0.5;
                positions[5] = z;

                geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

                const material = new THREE.LineBasicMaterial({
                    color: 0x4a90e2,
                    transparent: true,
                    opacity: 0.6,
                    linewidth: 1
                });

                const line = new THREE.Line(geometry, material);
                rainGroup.add(line);
            }

            return rainGroup;
        }

        function createSunEffect() {
            const sunGeometry = new THREE.SphereGeometry(1, 32, 32);
            const sunMaterial = new THREE.MeshBasicMaterial({
                color: 0xffdd00,
                emissive: 0xffdd00,
                emissiveIntensity: 0.5
            });
            const sun = new THREE.Mesh(sunGeometry, sunMaterial);
            sun.position.set(5, 8, -5);

            // Sun rays
            const rayGeometry = new THREE.ConeGeometry(0.1, 5, 4);
            const rayMaterial = new THREE.MeshBasicMaterial({
                color: 0xffdd00,
                transparent: true,
                opacity: 0.3
            });

            const rays = new THREE.Group();
            for (let i = 0; i < 8; i++) {
                const ray = new THREE.Mesh(rayGeometry, rayMaterial);
                ray.position.copy(sun.position);
                ray.rotation.z = (Math.PI * 2 / 8) * i;
                rays.add(ray);
            }

            const sunGroup = new THREE.Group();
            sunGroup.add(sun);
            sunGroup.add(rays);

            return sunGroup;
        }

        // Camera position
        camera.position.set(0, 5, 12);
        camera.lookAt(0, 0, 0);

        // Data source handlers
        const sourceButtons = document.querySelectorAll('.source-button');
        let activeSource = null;

        const mockData = {
            cap: {
                alerts: 3,
                coverage: 2500,
                responseTime: '2m',
                weatherAdvisory: 'Severe thunderstorm warning in effect for Greater Accra Region. Heavy rainfall expected.',
                showWeather: true
            },
            meteo: {
                alerts: 1,
                coverage: 5000,
                responseTime: '1m',
                weatherAdvisory: 'Clear skies expected. Temperature: 28°C. Humidity: 65%.',
                showWeather: true,
                sunny: true
            },
            agro: {
                alerts: 2,
                coverage: 8000,
                responseTime: '5m',
                agroAdvisory: 'Optimal planting conditions for maize and cassava. Soil moisture levels adequate.',
                showAgro: true
            },
            nadmo: {
                alerts: 5,
                coverage: 3000,
                responseTime: '3m',
                weatherAdvisory: 'Flood risk advisory for low-lying areas. Emergency teams on standby.',
                showWeather: true
            },
            fire: {
                alerts: 2,
                coverage: 1500,
                responseTime: '4m',
                weatherAdvisory: 'High fire risk due to dry conditions. Avoid open flames.',
                showWeather: true
            },
            police: {
                alerts: 4,
                coverage: 2000,
                responseTime: '2m',
                weatherAdvisory: 'Traffic advisory: Major routes affected by weather conditions.',
                showWeather: true
            }
        };

        sourceButtons.forEach(button => {
            button.addEventListener('click', () => {
                const source = button.dataset.source;

                // Update active state
                sourceButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // Update data
                const data = mockData[source];
                document.getElementById('active-alerts').textContent = data.alerts;
                document.getElementById('coverage').textContent = data.coverage;
                document.getElementById('response-time').textContent = data.responseTime;

                // Update advisories
                const weatherAdvisory = document.getElementById('weather-advisory');
                const agroAdvisory = document.getElementById('agro-advisory');

                // Show/hide advisories based on data
                if (data.showWeather) {
                    weatherAdvisory.style.display = 'block';
                    weatherAdvisory.style.opacity = '1';
                } else {
                    weatherAdvisory.style.display = 'none';
                }

                if (data.showAgro) {
                    agroAdvisory.style.display = 'block';
                    agroAdvisory.style.opacity = '1';
                } else {
                    agroAdvisory.style.display = 'none';
                }

                if (data.weatherAdvisory) {
                    document.getElementById('weather-text').textContent = data.weatherAdvisory;
                }
                if (data.agroAdvisory) {
                    document.getElementById('agro-text').textContent = data.agroAdvisory;
                }

                // Update weather effect
                if (currentWeatherEffect) {
                    scene.remove(currentWeatherEffect);
                }

                if (data.sunny) {
                    currentWeatherEffect = createSunEffect();
                } else if (data.showWeather && source !== 'fire') {
                    currentWeatherEffect = createRainEffect();
                }

                if (currentWeatherEffect) {
                    scene.add(currentWeatherEffect);
                }

                // Animate status update
                gsap.from('.status-value', {
                    scale: 0,
                    duration: 0.5,
                    stagger: 0.1,
                    ease: 'back.out(1.7)'
                });
            });
        });

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);

            // Rotate map slowly
            ghanaMap.rotation.z += 0.001;
            wireframe.rotation.z += 0.001;

            // Animate rain
            if (currentWeatherEffect && currentWeatherEffect.children) {
                currentWeatherEffect.children.forEach(line => {
                    const positions = line.geometry.attributes.position.array;
                    positions[1] -= 0.3; // Move top point down
                    positions[4] -= 0.3; // Move bottom point down

                    // Reset rain when it goes below ground
                    if (positions[4] < -5) {
                        positions[1] = Math.random() * 15;
                        positions[4] = positions[1] - 0.5;
                    }

                    line.geometry.attributes.position.needsUpdate = true;
                });
            }

            // Animate sun rays
            if (currentWeatherEffect && currentWeatherEffect.children && currentWeatherEffect.children[0] && currentWeatherEffect.children[0].type === 'Mesh') {
                currentWeatherEffect.rotation.y += 0.01;
            }

            renderer.render(scene, camera);
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // Start animation
        animate();

        // Hide loading screen
        setTimeout(() => {
            document.getElementById('loading').classList.add('hidden');

            // Initial animation
            gsap.from('.header h1', {
                y: -50,
                opacity: 0,
                duration: 1,
                ease: 'power3.out'
            });

            gsap.from('.glass-card', {
                scale: 0.8,
                opacity: 0,
                duration: 0.8,
                stagger: 0.1,
                ease: 'power3.out',
                delay: 0.3
            });
        }, 1000);

        // Initialize visibility
        function initializeVisibility() {
            // Ensure all glass-card elements are visible
            const glassCards = document.querySelectorAll('.glass-card');
            glassCards.forEach(card => {
                card.style.opacity = '1';
                card.style.visibility = 'visible';
            });

            // Show weather advisory by default
            const weatherAdvisory = document.getElementById('weather-advisory');
            weatherAdvisory.style.display = 'block';
            weatherAdvisory.style.opacity = '1';
        }

        // Initialize visibility immediately
        initializeVisibility();

        // Auto-select first source
        setTimeout(() => {
            sourceButtons[0].click();
        }, 1500);
    </script>
</body>
</html>