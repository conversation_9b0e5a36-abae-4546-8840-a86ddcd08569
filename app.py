import os
import uuid
from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit # For future Pi communication
import requests
from bs4 import BeautifulSoup # For web scraping
import xml.etree.ElementTree as ET # For parsing CAP XML
from datetime import datetime, timezone, timedelta
import json
import logging

# --- Flask App Setup ---
app = Flask(__name__) # Use default templates folder
app.config['SECRET_KEY'] = 'emergency_secret_key!' # CHANGE THIS!
socketio = SocketIO(app) # For future Pi client updates

# --- Configuration & Constants ---
# Open-Meteo API
OPEN_METEO_API_URL = "https://api.open-meteo.com/v1/forecast"
ACCRA_LATITUDE = 5.55602
ACCRA_LONGITUDE = -0.1969
DEFAULT_LOCATION_DESC = "Accra, Greater Accra Region, Ghana"

# GHAAP Agro-Climate (Example URL - make this configurable or dynamic later)
# Default: Ashanti region, Ejisu Juaben district, Maize, current year
GHAAP_DEFAULT_REGION = "REG02" # Ashanti
GHAAP_DEFAULT_DISTRICT = "DS028" # Ejisu Juaben
GHAAP_DEFAULT_CROP = "CT0000000008" # Maize
GHAAP_BASE_URL = "https://ghaap.com/weather-forecast/index.php"

# CAP Message Sender Information (for internally generated messages if any)
CAP_SENDER_ID = "<EMAIL>"
CAP_SENDER_NAME = "Emergency Communications Platform - HQ"

# Logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Data Storage (Simple In-Memory) ---
# Stores the latest processed data for each source type
latest_data_store = {
    "cap": None,
    "meteo": None,
    "agro": None
}

# --- Helper Functions (Open-Meteo & CAP Parsing - adapted from cap_generator_py) ---
def get_utc_timestamp():
    return datetime.now(timezone.utc).isoformat(timespec='seconds')

def interpret_weather_code(code):
    codes = {
        0: "Clear sky", 1: "Mainly clear", 2: "Partly cloudy", 3: "Overcast",
        45: "Fog", 48: "Depositing rime fog",
        51: "Light drizzle", 53: "Moderate drizzle", 55: "Dense drizzle",
        61: "Slight rain", 63: "Moderate rain", 65: "Heavy rain",
        71: "Slight snow fall", 73: "Moderate snow fall", 75: "Heavy snow fall",
        80: "Slight rain showers", 81: "Moderate rain showers", 82: "Violent rain showers",
        95: "Thunderstorm", 96: "Thunderstorm with hail", 99: "Thunderstorm with heavy hail"
    }
    return codes.get(code, f"Weather code: {code}")

def fetch_open_meteo_data(lat=ACCRA_LATITUDE, lon=ACCRA_LONGITUDE, location_desc=DEFAULT_LOCATION_DESC):
    params = {
        "latitude": lat,
        "longitude": lon,
        "current_weather": "true",
        "hourly": "temperature_2m,relativehumidity_2m,apparent_temperature,precipitation_probability,weathercode,windspeed_10m,winddirection_10m",
        "daily": "weathercode,temperature_2m_max,temperature_2m_min,precipitation_sum,precipitation_probability_max",
        "timezone": "auto"
    }
    try:
        response = requests.get(OPEN_METEO_API_URL, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        current_weather = data.get("current_weather", {})
        daily_weather = data.get("daily", {})

        # Construct a user-friendly advisory
        temp = current_weather.get('temperature', 'N/A')
        windspeed = current_weather.get('windspeed', 'N/A')
        weather_code = current_weather.get('weathercode')
        condition = interpret_weather_code(weather_code)

        humidity = "N/A"
        if "hourly" in data and "relativehumidity_2m" in data["hourly"] and data["hourly"]["relativehumidity_2m"]:
            humidity = data["hourly"]["relativehumidity_2m"][0] # Example: first hour's humidity

        precip_prob = "N/A"
        if "hourly" in data and "precipitation_probability" in data["hourly"] and data["hourly"]["precipitation_probability"]:
             precip_prob = data["hourly"]["precipitation_probability"][0]


        advisory_text = (
            f"Weather for {location_desc}: {condition}. "
            f"Temp: {temp}°C. Humidity: {humidity}%. "
            f"Wind: {windspeed} km/h. Precipitation Chance: {precip_prob}%."
        )

        # Data for UI and Pi
        processed_data = {
            "source": "meteo",
            "location": location_desc,
            "condition": condition,
            "temperature": temp,
            "humidity": humidity,
            "windspeed": windspeed,
            "precipitation_probability": precip_prob,
            "weather_code": weather_code, # For potential icon mapping in UI
            "advisory_text": advisory_text, # For UI display
            "audio_text": f"Weather update for {location_desc}. Condition: {condition}. Temperature is {temp} degrees Celsius.", # For TTS
            "timestamp": get_utc_timestamp(),
            "icon_emoji": "🌤️" # Default, can be changed based on weather_code
        }

        # Basic icon logic (can be expanded)
        if weather_code is not None:
            if weather_code in [0, 1]: processed_data["icon_emoji"] = "☀️" # Sunny
            elif weather_code in [2, 3]: processed_data["icon_emoji"] = "☁️" # Cloudy
            elif weather_code >= 51 and weather_code <= 67: processed_data["icon_emoji"] = "🌧️" # Rainy
            elif weather_code >= 95 : processed_data["icon_emoji"] = "⛈️" # Thunderstorm

        latest_data_store["meteo"] = processed_data
        return processed_data

    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching Open-Meteo data: {e}")
        return {"error": str(e), "advisory_text": "Could not fetch weather data."}
    except Exception as e:
        logging.error(f"Error processing Open-Meteo data: {e}")
        return {"error": str(e), "advisory_text": "Error processing weather data."}


def parse_cap_alert_xml(cap_xml_string):
    try:
        root = ET.fromstring(cap_xml_string)
        ns = {'cap': 'urn:oasis:names:tc:emergency:cap:1.2'}

        # Extract key elements
        identifier = root.find('cap:identifier', ns).text if root.find('cap:identifier', ns) is not None else str(uuid.uuid4())
        sender = root.find('cap:sender', ns).text if root.find('cap:sender', ns) is not None else CAP_SENDER_ID
        sent_time_cap = root.find('cap:sent', ns).text if root.find('cap:sent', ns) is not None else get_utc_timestamp()

        # CAP allows multiple <info> blocks, process the first one for simplicity
        info_block = root.find('cap:info', ns)
        if not info_block:
            return {"error": "No <info> block found in CAP XML.", "advisory_text": "Invalid CAP format."}

        headline = info_block.find('cap:headline', ns).text if info_block.find('cap:headline', ns) is not None else "N/A"
        description = info_block.find('cap:description', ns).text if info_block.find('cap:description', ns) is not None else "N/A"
        area_desc_element = info_block.find('cap:area/cap:areaDesc', ns)
        area_description = area_desc_element.text if area_desc_element is not None else "Not specified"
        event = info_block.find('cap:event', ns).text if info_block.find('cap:event', ns) is not None else "Alert"

        advisory_text = f"CAP Alert: {headline}. Description: {description}. Area: {area_description}."
        audio_text = f"Emergency Alert. {event}. {headline}. {description}. Area affected: {area_description}."

        processed_data = {
            "source": "cap",
            "identifier": identifier,
            "sender": sender,
            "sent_time_cap": sent_time_cap, # Time from CAP message
            "headline": headline,
            "description": description,
            "area_description": area_description,
            "event": event,
            "advisory_text": advisory_text, # For UI display
            "audio_text": audio_text,       # For TTS
            "timestamp": get_utc_timestamp(), # Time processed by this system
            "icon_emoji": "⚡"
        }
        latest_data_store["cap"] = processed_data
        return processed_data

    except ET.ParseError as e:
        logging.error(f"Error parsing CAP XML: {e}")
        return {"error": f"Invalid CAP XML format: {e}", "advisory_text": "Invalid CAP XML provided."}
    except Exception as e:
        logging.error(f"Unexpected error processing CAP XML: {e}")
        return {"error": str(e), "advisory_text": "Error processing CAP alert."}


def fetch_ghaap_agro_data(region=GHAAP_DEFAULT_REGION, district=GHAAP_DEFAULT_DISTRICT, crop=GHAAP_DEFAULT_CROP):
    current_year = datetime.now().year
    params = {
        "p": "crop-search-event",
        "regiontxt": region,
        "districttxt": district,
        "crop": crop,
        "yeartxt": str(current_year) # Use current year
    }
    try:
        response = requests.get(GHAAP_BASE_URL, params=params, timeout=15)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'html.parser')

        # --- Web Scraping Logic ---
        # This is highly dependent on GHAAP.com's HTML structure and might break if they change it.
        # You'll need to inspect their page to find the correct selectors for the advisory.
        # Example: Look for a div with a specific class or ID that contains the advisory.
        advisory_text_parts = []

        # Try to find a common container for advisories. This is a guess.
        # Common selectors might be: 'div.advisory-content', 'table.crop-advisory', 'div#summary-section p'
        # Let's try a few common patterns.

        # Attempt 1: A specific div often used for content
        advisory_div = soup.find('div', class_='col-md-9') # This is a very generic selector from GHAAP
        if advisory_div:
            # Look for paragraphs or relevant text within this div
            # Often advisories are in <p> tags or within tables.
            # This part needs careful inspection of the actual GHAAP page for the specific crop search.
            paragraphs = advisory_div.find_all('p')
            if paragraphs:
                for p in paragraphs:
                    text = p.get_text(separator=' ', strip=True)
                    if text and len(text) > 30 : # Filter out very short/irrelevant paragraphs
                        advisory_text_parts.append(text)
            else: # If no paragraphs, maybe it's in table cells or list items
                list_items = advisory_div.find_all('li')
                for li in list_items:
                     text = li.get_text(separator=' ', strip=True)
                     if text and len(text) > 20:
                         advisory_text_parts.append(text)
                if not advisory_text_parts: # Still nothing? Try divs with text
                    text_divs = advisory_div.find_all('div', string=True) # Divs that directly contain text
                    for td in text_divs:
                        text = td.get_text(separator=' ', strip=True)
                        if text and len(text) > 20:
                            advisory_text_parts.append(text)


        if not advisory_text_parts:
            # Fallback if specific selectors fail - try to get some prominent text
            main_content = soup.find('body') # Last resort, search whole body
            if main_content:
                 paragraphs = main_content.find_all('p', limit=5) # Limit to avoid too much text
                 for p in paragraphs:
                    text = p.get_text(separator=' ', strip=True)
                    if "advisory" in text.lower() or "recommendation" in text.lower() or "forecast" in text.lower():
                         advisory_text_parts.append(text)
            if not advisory_text_parts:
                advisory_text_parts.append("Could not automatically extract detailed agro-advisory. Please check the GHAAP website directly.")

        advisory_summary = " ".join(advisory_text_parts[:3]) # Join first few relevant parts
        if len(advisory_summary) > 500: advisory_summary = advisory_summary[:500] + "..."


        processed_data = {
            "source": "agro",
            "region_code": region, # For reference
            "district_code": district,
            "crop_code": crop,
            "year": current_year,
            "advisory_text": advisory_summary if advisory_summary else "No specific advisory found. Check GHAAP website.",
            "audio_text": f"Agro-climate advisory from GHAAP. {advisory_summary if advisory_summary else 'Please check the GHAAP website for details.'}",
            "timestamp": get_utc_timestamp(),
            "icon_emoji": "🌾"
        }
        latest_data_store["agro"] = processed_data
        return processed_data

    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching GHAAP data: {e}")
        return {"error": str(e), "advisory_text": "Could not fetch GHAAP agro-advisory."}
    except Exception as e:
        logging.error(f"Error processing GHAAP data: {e}")
        return {"error": str(e), "advisory_text": "Error processing GHAAP agro-advisory."}


# --- Flask Routes ---
@app.route('/')
def index():
    """Serves the main HTML page."""
    return render_template('Home.html') # Assumes claude.html is in 'templates' or current dir

@app.route('/fetch_data', methods=['GET'])
def handle_fetch_data():
    source = request.args.get('source', '').lower()
    logging.info(f"Received request to fetch data for source: {source}")
    data = None
    if source == 'meteo':
        # Could add lat/lon from request.args if needed
        data = fetch_open_meteo_data()
    elif source == 'agro':
        # Could add region/district/crop from request.args if needed
        data = fetch_ghaap_agro_data()
    else:
        return jsonify({"error": "Invalid data source specified."}), 400

    if data and "error" not in data:
        # Emit to Pi clients (future enhancement)
        # socketio.emit('new_alert_data', data, namespace='/pi_client')
        return jsonify(data)
    else:
        return jsonify(data if data else {"error": "Failed to fetch data for unknown reason."}), 500

@app.route('/submit_cap', methods=['POST'])
def handle_submit_cap():
    try:
        cap_xml = request.data.decode('utf-8')
        if not cap_xml:
            return jsonify({"error": "No CAP XML data received."}), 400

        logging.info(f"Received CAP XML for processing. Length: {len(cap_xml)}")
        data = parse_cap_alert_xml(cap_xml)

        if data and "error" not in data:
            # Emit to Pi clients (future enhancement)
            # socketio.emit('new_alert_data', data, namespace='/pi_client')
            return jsonify(data)
        else:
            return jsonify(data if data else {"error": "Failed to parse CAP XML."}), 400
    except Exception as e:
        logging.error(f"Error in /submit_cap endpoint: {e}")
        return jsonify({"error": f"Server error processing CAP: {str(e)}"}), 500

# --- SocketIO Events (for future Pi client) ---
@socketio.on('connect', namespace='/pi_client') # Example namespace for Pi clients
def handle_pi_connect():
    logging.info(f"Raspberry Pi client connected: {request.sid}")
    # Optionally send current state or a welcome message
    # emit('connection_ack', {'message': 'Connected to Emergency Platform Server'})

@socketio.on('disconnect', namespace='/pi_client')
def handle_pi_disconnect():
    logging.info(f"Raspberry Pi client disconnected: {request.sid}")


# --- Main Execution ---
if __name__ == '__main__':
    logging.info("Starting Emergency Communications Platform server...")
    # For development: flask run --host=0.0.0.0 --port=5000
    # Or using socketio.run for integrated SocketIO development server:
    socketio.run(app, host='0.0.0.0', port=5000, debug=True, use_reloader=True)
